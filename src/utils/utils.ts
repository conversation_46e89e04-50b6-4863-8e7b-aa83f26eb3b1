import { message } from "antd";

// 给EasyForm传入的data使用
export const easyFormInputsData = (blocks: any, type: 'show' | 'run', needFormResult = false) => {
    // const status = blocks.status
    const inputs = blocks.inputs;
    const input_arr = blocks.input_arr;
    let data: any = {};

    // show 卡片传入input_arr数据
    if (type == 'run') {
        return inputs;
    }


    input_arr?.forEach((item: any) => {
        const value = item.treeValue;
        const defaultvalue = (value?.indexOf('-name') > -1 ? value?.substring(0, value?.indexOf('-name')).replace(/^"|"$/g, '') : value?.substring(0, value?.indexOf('0&&')));
        data[item.key] = defaultvalue;
        // if ((Object.keys(inputs))?.length > 0) {
        //     data[item.key] = inputs?.[item.key] || defaultvalue;
        // } else {
        //     // data[item.key] = (value?.indexOf('-name') > -1 ? value?.substring(0, value?.indexOf('-name')).replace(/^"|"$/g, '') : value?.substring(0, value?.indexOf('0&&')));
        //     data[item.key] = defaultvalue;

        // }
        // const value = item.treeValue;
        // data[item.key] = (value?.indexOf('-name') > -1 ? value?.substring(0, value?.indexOf('-name')).replace(/^"|"$/g, '') : value?.substring(0, value?.indexOf('0&&')));

        // 调式到运行组件，使用运行结果中的值
        // if (type == 'run') {
        //     data[item.key] = inputs[item.key];
        // }
        // if (type == 'debug') {
        //     if (needFormResult) {
        //         data[item.key] = Object.prototype.hasOwnProperty.call(inputs, item.key) ? inputs[item.key] : (value?.indexOf('-name') > -1 ? value?.substring(0, value?.indexOf('-name')).replace(/^"|"$/g, '') : value?.substring(0, value?.indexOf('0&&')));
        //     }

        //     if (!needFormResult) {
        //         data[item.key] = value?.indexOf('-name') > -1 ? value?.substring(0, value?.indexOf('-name')).replace(/^"|"$/g, '') : value?.substring(0, value?.indexOf('0&&'));
        //     }
        // }
        // if (Object.prototype.hasOwnProperty.call(inputs, item.key)) {
        //     data[item.key] = inputs[item.key]
        // } else {
        //     data[item.key] = ''
        // }

    })
    console.log("🚀 ~ input_arr?.forEach ~ data:", data)
    return data;
}

// 处理input_arr里面treeValue
export const getInputArrTreeValue = (value: any) => {
    const defaultValue = (value?.indexOf('-name') > -1 ? value?.substring(0, value?.indexOf('-name')).replace(/^"|"$/g, '') : value?.substring(0, value?.indexOf('0&&')));
    return defaultValue;
}

// 格式化treeValue的字符串
export const getFormatTreeValue = (str: any) => {
    if (typeof str === 'object' && str) {
        if (str instanceof Array) {
            // 数组
            return {
                type: 'array',
                obj: str
            };
        } else {
            // 对象
            return {
                type: 'object',
                obj: str
            };
        }
    } else {
        try {
            const obj = JSON.parse(str);
            if (typeof obj === 'object' && obj) {
                if (obj instanceof Array) {
                    // 数组
                    return {
                        type: 'array',
                        obj: obj
                    };
                } else {
                    // 对象
                    return {
                        type: 'object',
                        obj: obj
                    };
                }
            } else {
                // 字符串 数字 布尔
                if(str == 'true' || str == 'false') {
                    return {
                        type: 'boolean',
                        obj: str == 'true' ? true : false
                    };
                }
                return {
                    type: typeof (str),
                    obj: str
                };
            }
        } catch (e) {
            return {
                type: 'string',
                obj: str
            };
        }
    }
}

export const copyToClipboard = (text: any) => {
    // 创建一个临时的 textarea 元素
    const textarea = document.createElement("textarea");
    // 设置 textarea 的值为要复制的文本
    textarea.value = text;
    // 将 textarea 元素添加到文档中
    document.body.appendChild(textarea);
    // 选中 textarea 中的文本
    textarea.select();
    // 执行复制命令
    document.execCommand('copy');
    // 移除临时的 textarea 元素
    document.body.removeChild(textarea);
    message.success('复制成功');
  }

export const extractTextUsingDOMParser = (html: any) => {
  const parser = new DOMParser();
  const doc = parser.parseFromString(html, 'text/html');
  // 返回整个文档的文本内容
  return doc.body.textContent || '';
}


/**
 * 深克隆函数，支持多种数据类型
 * @param {*} target - 需要克隆的目标
 * @param {WeakMap} map - 用于循环引用检测的 WeakMap
 * @returns {*} - 克隆后的对象
 */
export const  deepClone = (target, map = new WeakMap()) => {
  // 处理基本数据类型和函数
  if (typeof target !== 'object' || target === null) {
    return target;
  }

  // 处理日期对象
  if (target instanceof Date) {
    return new Date(target.getTime());
  }

  // 处理正则表达式对象
  if (target instanceof RegExp) {
    return new RegExp(target);
  }

  // 处理 Set 对象
  if (target instanceof Set) {
    const cloneSet = new Set();
    target.forEach(value => {
      cloneSet.add(deepClone(value, map));
    });
    return cloneSet;
  }

  // 处理 Map 对象
  if (target instanceof Map) {
    const cloneMap = new Map();
    target.forEach((value, key) => {
      cloneMap.set(key, deepClone(value, map));
    });
    return cloneMap;
  }

  // 处理 DOM 节点
  if (target instanceof Node) {
    return target.cloneNode(true);
  }

  // 检查循环引用
  if (map.has(target)) {
    return map.get(target);
  }

  // 处理普通对象和数组
  const cloneTarget = Array.isArray(target) ? [] : {};
  // 保存到 WeakMap 中，用于检测循环引用
  map.set(target, cloneTarget);

  // 处理对象的 Symbol 类型属性
  const symbolKeys = Object.getOwnPropertySymbols(target);
  for (const symbolKey of symbolKeys) {
    cloneTarget[symbolKey] = deepClone(target[symbolKey], map);
  }

  // 处理对象的所有可枚举属性
  for (const key in target) {
    if (Object.prototype.hasOwnProperty.call(target, key)) {
      cloneTarget[key] = deepClone(target[key], map);
    }
  }

  return cloneTarget;
}